<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DDBrain</title>
    <script src="/config.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.0.2/marked.min.js"></script>
    <style>
        /* Root variables */
        :root {
            --ddb-yellow: #FFD100;
            --ddb-black: #000000;
            --ddb-gray: #2D2D2D;
            --ddb-light-gray: #F5F5F5;
            --bg-primary: #ffffff;
            --bg-secondary: #f5f5f5;
            --text-primary: #000000;
            --text-secondary: #4b5563;
            --border-color: #e5e7eb;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* Dark mode variables */
        [data-theme="dark"] {
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --text-primary: #ffffff;
            --text-secondary: #e5e7eb;
            --border-color: #404040;
            --shadow-color: rgba(0, 0, 0, 0.3);
        }

        /* Base styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        /* Login screen */
        #loginScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--ddb-black);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 50;
        }

        .login-container {
            background-color: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .login-logo {
            width: 120px;
            height: auto;
            margin: 0 auto 1.5rem;
            display: block;
            object-fit: contain;
        }

        .login-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--ddb-yellow);
        }

        .login-input {
            width: 100%;
            padding: 0.75rem;
            margin-bottom: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            transition: all 0.2s;
        }

        .login-input:focus {
            border-color: var(--ddb-yellow);
            outline: none;
            box-shadow: 0 0 0 2px rgba(255, 209, 0, 0.2);
        }

        .login-btn {
            width: 100%;
            padding: 0.75rem;
            background-color: var(--ddb-yellow);
            color: var(--ddb-black);
            font-weight: 500;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            transition: all 0.2s;
        }

        .login-btn:hover {
            background-color: #e6bc00;
        }

        .guest-btn {
            width: 100%;
            padding: 0.75rem;
            background-color: #e5e7eb;
            color: #374151;
            font-weight: 500;
            border-radius: 0.375rem;
            transition: all 0.2s;
        }

        .guest-btn:hover {
            background-color: #d1d5db;
        }

        /* Hide main content initially */
        #mainContent {
            display: none;
            padding-top: 4rem; /* Add padding-top to offset fixed header */
        }

        /* Header styles */
        .fixed-header {
            position: fixed; /* Changed from sticky to fixed */
            top: 0;
            left: 0; /* Added left: 0 */
            right: 0; /* Added right: 0 */
            background-color: var(--ddb-black);
            z-index: 40;
            border-bottom: 1px solid var(--ddb-yellow);
            padding: 0.75rem 1rem;
        }

        /* Burger menu */
        .burger-menu {
            display: none;
            cursor: pointer;
            padding: 0.75rem;
            margin-right: 1rem;
        }

        .burger-menu div {
            width: 28px;
            height: 3px;
            background-color: white;
            margin: 7px 0;
            transition: all 0.3s ease;
        }

        /* Sidebar toggle button */
        .sidebar-toggle {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-right: 1rem;
        }

        .sidebar-toggle svg {
            width: 28px;
            height: 28px;
            fill: currentColor;
        }

        /* Logo and title */
        .logo-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-container img {
            height: 2.5rem;
            width: auto;
        }

        /* Main content area */
        .content-wrapper {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1rem;
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 2rem;
            height: calc(100vh - 4rem);
            transition: grid-template-columns 0.3s ease;
        }

        .content-wrapper.sidebar-hidden {
            grid-template-columns: 0 1fr;
        }

        /* Sidebar styles */
        #sidebar {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            height: 100%;
            overflow-y: auto;
            min-width: 400px;
            transition: transform 0.3s ease, opacity 0.3s ease;
        }

        .sidebar-hidden #sidebar {
            transform: translateX(-100%);
            opacity: 0;
            pointer-events: none;
        }

        /* Document list styles */
        #documentList .document-item {
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            margin-bottom: 0.75rem;
            background-color: white;
            transition: all 0.2s;
            word-wrap: break-word;
            overflow-wrap: break-word;
            word-break: break-word;
        }

        #documentList .document-item:hover {
            border-color: var(--ddb-yellow);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .document-checkbox {
            margin-right: 0.75rem;
            width: 1.2rem;
            height: 1.2rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.25rem;
            cursor: pointer;
        }

        .document-checkbox:checked {
            background-color: var(--ddb-yellow);
            border-color: var(--ddb-yellow);
        }

        .select-all-container {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.5rem;
            background-color: #f9fafb;
            border-radius: 0.375rem;
        }

        .delete-selected-btn {
            display: none;
            background-color: #ef4444;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s;
            margin-left: auto;
        }

        .delete-selected-btn:hover {
            background-color: #dc2626;
        }

        #documentList .document-item h3 {
            color: var(--ddb-black);
            word-wrap: break-word;
            overflow-wrap: break-word;
            word-break: break-word;
            max-width: 100%;
        }

        #documentList .document-item p {
            color: #6b7280;
            word-wrap: break-word;
            overflow-wrap: break-word;
            word-break: break-word;
            max-width: 100%;
        }

        /* Chat container */
        #chatContainer {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            height: 100%; /* Ensure it takes full height of its grid cell */
            display: flex;
            flex-direction: column;
            overflow: hidden; /* Prevent inner content from overflowing */
        }

        /* Chat messages */
        #chatMessages {
            flex: 1 1 auto; /* Allow chat messages to grow and shrink */
            overflow-y: auto; /* Enable scrolling for messages */
            margin-bottom: 1rem; /* Keep some space before the input */
        }

        /* Input container */
        .chat-input-container { /* New container for input + send */
            flex-shrink: 0; /* Prevent input area from shrinking */
            display: flex;
            gap: 0.5rem; /* Use gap instead of margin */
            align-items: center; /* Align items vertically */
        }

        /* Input field adjustments */
        #queryInput {
            flex-grow: 1; /* Allow input to take available space */
            margin-bottom: 0; /* Remove bottom margin as gap handles spacing */
        }

        /* Send button adjustments */
        #sendButton {
            flex-shrink: 0; /* Prevent button from shrinking */
        }

        /* Buttons */
        .btn-primary {
            background-color: var(--ddb-yellow);
            color: var(--ddb-black);
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background-color: #e6bc00;
        }

        .btn-secondary {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s;
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background-color: var(--bg-primary);
        }

        /* Input styles */
        .input-field {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            transition: all 0.2s;
            background-color: var(--bg-primary);
        }

        .input-field:focus {
            border-color: var(--ddb-yellow);
            outline: none;
            box-shadow: 0 0 0 2px rgba(255, 209, 0, 0.2);
        }

        .input-field::placeholder {
            color: var(--text-secondary);
        }

        /* Source citations */
        .source-citation {
            background-color: #f8fafc;
            border-left: 3px solid var(--ddb-yellow);
            padding: 1rem;
            margin: 1rem 0;
            font-size: 0.875rem;
        }

        .source-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            font-weight: 500;
        }

        .source-header:hover {
            color: var(--ddb-black);
        }

        .source-content {
            margin-top: 0.5rem;
            padding-left: 1.5rem;
            display: none;
        }

        .source-arrow {
            transition: transform 0.2s;
            display: inline-block;
            width: 20px;
            height: 20px;
            text-align: center;
            line-height: 20px;
        }

        .source-arrow.expanded {
            transform: rotate(90deg);
        }

        /* Source citations and recommended prompts sections */
        .sources-section, .prompts-section {
            display: block;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #e5e7eb;
        }

        .sources-content, .prompts-content {
            display: block;
            margin-top: 1rem;
        }

        .sources-header, .prompts-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            font-weight: 600;
            color: var(--ddb-black);
            margin-bottom: 1rem;
        }

        .sources-header:hover, .prompts-header:hover {
            opacity: 0.8;
        }

        .prompts-content {
            display: none;
        }

        /* Recommended prompts */
        .recommended-prompt {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            background-color: #f3f4f6;
            border-radius: 1rem;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .recommended-prompt:hover {
            background-color: var(--ddb-yellow);
            color: var(--ddb-black);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .content-wrapper {
                grid-template-columns: 1fr;
                height: calc(100vh - 4rem);
                padding: 0;
                overflow: hidden;
            }

            #chatContainer {
                border-radius: 0;
                height: 100%;
                position: relative;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            #chatMessages {
                flex: 1;
                overflow-y: auto;
                padding: 1rem;
                padding-bottom: 80px;
            }

            .chat-input-container {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                flex-shrink: 0;
                border-top: 1px solid var(--border-color);
                background-color: var(--bg-primary);
                padding: 1rem;
            }

            #sidebar {
                position: fixed;
                left: 0;
                top: 4rem;
                bottom: 0;
                z-index: 30;
                transform: translateX(-100%);
                opacity: 0;
            }

            #sidebar.mobile-visible {
                transform: translateX(0);
                opacity: 1;
            }

            .burger-menu {
                display: block;
            }

            .sidebar-toggle {
                display: none;
            }
        }

        /* Sidebar text colors */
        #sidebar h2, #sidebar h3 {
            color: var(--text-primary);
        }

        #sidebar p {
            color: var(--text-secondary);
        }

        #documentList .document-item h3 {
            color: var(--text-primary);
        }

        #documentList .document-item p {
            color: var(--text-secondary);
        }

        /* Chat functionality */
        #chatMessages .user-message {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        #chatMessages .assistant-message {
            background-color: var(--bg-primary);
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        /* Dark mode specific styles */
        [data-theme="dark"] .login-container {
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }

        [data-theme="dark"] .login-input {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .login-title {
            color: var(--text-primary);
        }

        [data-theme="dark"] #sidebar {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] #documentList .document-item {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] #documentList .document-item:hover {
            border-color: var(--ddb-yellow);
            background-color: var(--bg-primary);
        }

        [data-theme="dark"] #documentList .document-item h3 {
            color: var(--text-primary);
        }

        [data-theme="dark"] #documentList .document-item p {
            color: var(--text-secondary);
        }

        [data-theme="dark"] #chatContainer {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .chat-message {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        [data-theme="dark"] .user-message {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .assistant-message {
            background-color: var(--bg-primary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .input-field {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .source-citation {
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        [data-theme="dark"] .source-header {
            color: var(--text-primary);
        }

        [data-theme="dark"] .source-content {
            color: var(--text-secondary);
        }

        [data-theme="dark"] .recommended-prompt {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        [data-theme="dark"] .recommended-prompt:hover {
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }

        [data-theme="dark"] .sources-header,
        [data-theme="dark"] .prompts-header {
            color: var(--text-primary);
        }

        [data-theme="dark"] .btn-secondary {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .btn-secondary:hover {
            background-color: var(--bg-primary);
        }

        /* New chat button specific styles */
        #newChatBtn {
            background-color: transparent;
            padding: 0.5rem;
            position: relative;
        }

        #newChatBtn:hover {
            background-color: transparent;
        }

        /* Dark mode toggle button specific styles */
        #darkModeToggle {
            background-color: transparent;
            padding: 0.5rem;
            border: none;
            position: relative;
        }

        #darkModeToggle:hover {
            background-color: transparent;
        }

        #darkModeToggle svg {
            stroke: white;
            transition: stroke 0.2s;
        }

        #darkModeToggle:hover svg {
            stroke: var(--ddb-yellow);
        }

        /* Logout button specific styles */
        #logoutBtn {
            background-color: transparent;
            padding: 0.5rem;
            border: none;
            position: relative;
        }

        #logoutBtn:hover {
            background-color: transparent;
        }

        /* Upload button animations */
        @keyframes blink-upload {
            0% { stroke: white; }
            50% { stroke: var(--ddb-yellow); }
            100% { stroke: white; }
        }

        .uploading svg {
            animation: blink-upload 0.4s infinite;
        }

        /* Icon button base styles */
        .icon-btn {
            position: relative;
            background-color: transparent;
            padding: 0.5rem;
            border: none;
        }

        .icon-btn svg {
            stroke: white;
            transition: stroke 0.2s;
        }

        .icon-btn:hover svg {
            stroke: var(--ddb-yellow);
        }

        .icon-btn::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: -45px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 50;
        }

        .icon-btn::before {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            border-width: 6px;
            border-style: solid;
            border-color: transparent transparent rgba(0, 0, 0, 0.9) transparent;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 51;
        }

        .icon-btn:hover::after,
        .icon-btn:hover::before {
            opacity: 1;
            visibility: visible;
        }

        /* Add pulsating animation */
        @keyframes pulse-blink {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(2);
                opacity: 0.5;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .blink {
            animation: pulse-blink 1.2s ease-in-out infinite;
        }

        /* Update upload status styles */
        .upload-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .upload-status .status-icon {
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background-color: var(--ddb-yellow);
            display: inline-block;
        }

        .upload-status .status-icon.blink {
            animation: pulse-blink 1.2s ease-in-out infinite;
        }

        .chat-message {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
        }

        .chat-message .main-content-area {
            line-height: 1.6;
        }

        .chat-message .main-content-area p {
            margin-bottom: 1.25rem;
        }

        .chat-message .main-content-area ul,
        .chat-message .main-content-area ol {
            margin-bottom: 1.25rem;
            padding-left: 1.5rem;
        }

        .chat-message .main-content-area li {
            margin-bottom: 0.5rem;
        }

        .chat-message .main-content-area h1,
        .chat-message .main-content-area h2,
        .chat-message .main-content-area h3,
        .chat-message .main-content-area h4 {
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .chat-message .main-content-area strong,
        .chat-message .main-content-area b {
            font-weight: 600;
        }

        .chat-message .main-content-area blockquote {
            margin: 1.25rem 0;
            padding-left: 1rem;
            border-left: 4px solid var(--ddb-yellow);
            color: var(--text-secondary);
        }

        /* Ensure proper spacing between sections */
        .chat-message .sources-section,
        .chat-message .prompts-section {
            margin-top: 2rem;
            padding-top: 1.5rem;
        }

        /* Streaming cursor animation */
        .streaming-cursor {
            color: var(--ddb-yellow);
            font-weight: bold;
            animation: cursor-blink 1s infinite;
            margin-left: 2px;
        }

        @keyframes cursor-blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Smooth text appearance during streaming */
        .main-content-area {
            transition: all 0.1s ease;
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen">
        <div class="login-container">
            <img src="static/ddb-logo-login.png" alt="DDB Logo" class="login-logo" onerror="this.onerror=null; console.error('Failed to load login logo'); this.src='static/ddb-logo-80x80.png';">
            <h1 class="login-title">DDBrain</h1>
            <div class="space-y-4 mb-4">
                <div class="text-left text-gray-700 text-sm">
                    <p>Enter your Microsoft email below (optional hint):</p>
                </div>
                <input type="text" id="loginHintInput" class="login-input" placeholder="<EMAIL>" required>
            </div>
            <div class="mt-4">
                <a href="/login/microsoft" id="microsoftLoginBtn" class="block bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700">
                    Sign in with Microsoft
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div id="mainContent">
        <header class="fixed-header">
            <div class="max-w-7xl mx-auto flex justify-between items-center">
                <div class="flex items-center">
                    <button class="sidebar-toggle" id="sidebarToggle" style="padding: 0.25rem; margin-right: 0.25rem;">
                        <svg viewBox="0 0 24 24" class="w-6 h-6">
                            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                        </svg>
                    </button>
                    <div class="burger-menu" id="burgerMenu" style="margin-right: 0.25rem; padding: 0.25rem;">
                        <div></div>
                        <div></div>
                        <div></div>
                    </div>
                    <div class="logo-container" style="gap: 0.25rem;">
                        <img src="/static/ddb-logo-80x80.png" alt="DDB Logo" class="h-8 w-8">
                        <span class="text-xl font-bold" style="color: var(--ddb-yellow)">DDBrain</span>
                    </div>
                </div>
                <div class="flex items-center gap-1">
                    <button id="darkModeToggle" class="icon-btn" data-tooltip="Toggle Dark Mode">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path class="sun-icon hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                            <path class="moon-icon" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                        </svg>
                    </button>
                    <div id="adminControls" style="display: none;">
                        <!-- Removed file input and upload button -->
                    </div>
                    <button id="newChatBtn" class="icon-btn" data-tooltip="New Chat">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                        </svg>
                    </button>
                    <button id="logoutBtn" class="icon-btn" data-tooltip="Logout">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <main class="content-wrapper">
            <aside id="sidebar">
                <div class="mb-6">
                    <h2 class="text-xl font-semibold mb-4">Documents</h2>
                    <div class="flex flex-col gap-2">
                        <a href="https://ddbgroupcomph-my.sharepoint.com/personal/itstorage_ddbgroup_com_ph/_layouts/15/onedrive.aspx?id=%2Fpersonal%2Fitstorage%5Fddbgroup%5Fcom%5Fph%2FDocuments%2FDDB%20Group%20Repository&ga=1"
                           target="_blank"
                           rel="noopener noreferrer"
                           class="px-4 py-2 bg-[#FFD100] text-black rounded hover:bg-[#e6bc00] transition-colors text-center">
                            DDB Sharepoint Site
                        </a>
                        {% if is_admin %}
                            <a href="/sharepoint/sites"
                               class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-center">
                                Import SharePoint Files
                            </a>
                            <button id="clearDocumentsBtn" 
                                    class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors text-center">
                                Clear All Documents
                            </button>
                        {% endif %}
                    </div>
                </div>
                <div id="documentList" class="space-y-4">
                    <!-- Document items will be dynamically added here -->
                </div>
            </aside>

            <section id="chatContainer">
                <div id="chatMessages" class="flex-1 overflow-y-auto mb-4 space-y-4"></div>
                <form id="queryForm" class="chat-input-container">
                    <input type="text" id="queryInput" class="input-field" placeholder="Ask a question...">
                    <button type="submit" id="sendButton" class="btn-primary">Send</button>
                </form>
            </section>
        </main>
    </div>

    <script>
        // Health check configuration
        const HEALTH_CHECK_INTERVAL = 60000; // Check every minute
        let healthCheckTimer = null;

        // Start health checks after successful login
        function startHealthChecks() {
            healthCheckTimer = setInterval(async () => {
                try {
                    const response = await fetch(`${window.__RAG_BASE}/health`);
                    if (!response.ok) {
                        console.warn('Health check failed, server might be inactive');
                    }
                } catch (error) {
                    console.error('Health check failed:', error);
                    // Server might have shut down, show reconnection message
                    showReconnectionMessage();
                }
            }, HEALTH_CHECK_INTERVAL);
        }

        // Show reconnection message
        function showReconnectionMessage() {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'fixed top-0 left-0 w-full bg-yellow-100 text-yellow-800 px-4 py-3 text-center';
            messageDiv.innerHTML = `
                Server has shut down due to inactivity. 
                <button onclick="window.location.reload()" class="underline ml-2">
                    Click here to reconnect
                </button>
            `;
            document.body.prepend(messageDiv);
        }

        // Stop health checks on logout
        function stopHealthChecks() {
            if (healthCheckTimer) {
                clearInterval(healthCheckTimer);
                healthCheckTimer = null;
            }
        }

        // Login handling - Modified for Microsoft login hint
        // Remove the basic auth form submit listener
        // document.getElementById('loginForm').addEventListener('submit', async (e) => { ... });

        // Add listener to the Microsoft Login Button
        document.getElementById('microsoftLoginBtn').addEventListener('click', (e) => {
            e.preventDefault(); // Prevent default link behavior
            const hintInput = document.getElementById('loginHintInput');
            const loginHint = hintInput.value.trim();
            let redirectUrl = "/login/microsoft";

            if (loginHint) {
                redirectUrl += `?login_hint=${encodeURIComponent(loginHint)}`;
                console.log(`Microsoft login initiated with hint: ${loginHint}`);
            } else {
                console.log("Microsoft login initiated without hint.");
            }
            
            window.location.href = window.__RAG_BASE + redirectUrl; // Redirect to the constructed URL
        });

        // Document management
        // Removed upload-related event listeners

        // Add sidebar toggle functionality
        const sidebarToggle = document.getElementById('sidebarToggle');
        const burgerMenu = document.getElementById('burgerMenu');
        const contentWrapper = document.querySelector('.content-wrapper');
        const sidebar = document.getElementById('sidebar');

        function toggleSidebar() {
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('mobile-visible');
            } else {
                contentWrapper.classList.toggle('sidebar-hidden');
            }
        }

        sidebarToggle.addEventListener('click', toggleSidebar);
        burgerMenu.addEventListener('click', toggleSidebar);

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                sidebar.classList.remove('mobile-visible');
            }
        });

        // Update loadDocuments function to handle SharePoint documents
        async function loadDocuments() {
            const documentList = document.getElementById('documentList');
            documentList.innerHTML = ''; // Set to empty instead of showing loading text
            
            try {
                const response = await fetch(`${window.__RAG_BASE}/api/documents`, { credentials: 'include' });
                if (!response.ok) {
                    throw new Error('Failed to fetch documents');
                }
                const data = await response.json();
                
                if (data.documents && data.documents.length > 0) {
                    // Sort documents alphabetically by file name
                    const sortedDocs = data.documents.sort((a, b) => {
                        const nameA = (a.file_name || '').toLowerCase();
                        const nameB = (b.file_name || '').toLowerCase();
                        return nameA.localeCompare(nameB);
                    });

                    // Remove duplicates based on sharepoint_id
                    const uniqueDocs = sortedDocs.filter((doc, index, self) =>
                        index === self.findIndex((d) => (
                            d.sharepoint_id === doc.sharepoint_id
                        ))
                    );
                    
                    // Clear list before adding items
                    documentList.innerHTML = ''; 
                    
                    uniqueDocs.forEach(doc => {
                        const listItem = document.createElement('div');
                        listItem.className = 'document-item p-3 border border-gray-200 rounded mb-2';
                        const fileName = doc.file_name || `Document ID: ${doc.doc_id}` || 'Unnamed Document';
                        
                        // Create link if web_url exists
                        if (doc.web_url) {
                            const link = document.createElement('a');
                            link.href = doc.web_url;
                            link.textContent = fileName;
                            link.target = '_blank';
                            link.className = 'text-blue-600 hover:text-blue-800';
                            listItem.appendChild(link);
                        } else {
                            listItem.textContent = fileName;
                        }
                        
                        documentList.appendChild(listItem);
                    });
                } else {
                    documentList.innerHTML = '<p class="text-gray-500 text-center py-4">No documents indexed yet. Use the "Browse SharePoint" button to import documents.</p>';
                }
            } catch (error) {
                console.error('Error loading documents:', error);
                documentList.innerHTML = '<p class="text-red-500 text-center py-4">Error loading documents. Please try again later.</p>';
            }
        }

        // Add clear documents functionality
        document.addEventListener('DOMContentLoaded', () => {
            const clearBtn = document.getElementById('clearDocumentsBtn');
            if (clearBtn) {
                clearBtn.addEventListener('click', async () => {
                    if (confirm('Are you sure you want to clear all documents? This cannot be undone.')) {
                        try {
                            const response = await fetch(`${window.__RAG_BASE}/api/documents/clear`, {
                                method: 'POST',
                                credentials: 'include'
                            });
                            
                            if (response.ok) {
                                loadDocuments(); // Refresh the document list
                            } else {
                                throw new Error('Failed to clear documents');
                            }
                        } catch (error) {
                            console.error('Error clearing documents:', error);
                            alert('Failed to clear documents. Please try again.');
                        }
                    }
                });
            }
        });

        // Call loadDocuments when the page loads and after successful login
        document.addEventListener('DOMContentLoaded', async () => {
            // <<< Add a small delay to allow session to potentially update >>>
            // await new Promise(resolve => setTimeout(resolve, 100)); // Delay might not be needed with new approach

            // Check for login success flag in URL
            const urlParams = new URLSearchParams(window.location.search);
            const loginSuccessFlag = urlParams.get('login_success') === 'true';
            
            // Clean the flag from the URL using history.replaceState
            if (loginSuccessFlag) {
                const newUrl = window.location.pathname + window.location.hash; // Keep hash if present
                // window.history.replaceState({}, document.title, newUrl); // <<< TEMPORARILY COMMENT OUT
                console.log("Login success flag found in URL. (URL cleaning disabled for test)");
            }

            // Check if user is already authenticated via API OR if flag is present
            let isAuthenticated = loginSuccessFlag;
            if (!isAuthenticated) { // Only check API if flag wasn't present
                try {
                    console.log("Checking authentication status via /user endpoint...");
                    const response = await fetch(`${window.__RAG_BASE}/user`, { credentials: 'include' });
                    const userData = await response.json();
                    console.log("Authentication status from /user:", userData);
                    if (userData.authenticated) {
                        isAuthenticated = true;
                    }
                } catch (error) {
                    console.error('Error checking authentication status:', error);
                    // Assume not authenticated if API check fails
                }
            }

            // Show content based on final authentication status
            if (isAuthenticated) {
                console.log("User is authenticated (via flag or API), showing main content");
                document.getElementById('loginScreen').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
                
                // Start periodic tasks
                startDocumentRefresh();
                startHealthChecks();
            } else {
                console.log("User is not authenticated, showing login screen");
                document.getElementById('loginScreen').style.display = 'flex';
                document.getElementById('mainContent').style.display = 'none';
            }
        });

        // Function to create document item (simplified for SharePoint documents)
        function createDocumentItem(doc) {
            const div = document.createElement('div');
            div.className = 'document-item p-3 bg-white rounded-lg shadow-sm';
            
            div.innerHTML = `
                <div class="min-w-0">
                    <h3 class="font-medium text-gray-900 break-words">${doc.name}</h3>
                    <p class="text-sm text-gray-500 break-words">Type: ${doc.type || 'Document'}</p>
                    ${doc.source ? `<p class="text-sm text-gray-500">Source: ${doc.source}</p>` : ''}
                </div>
            `;

            return div;
        }

        // Chat functionality
        let currentResponse = '';
        let eventSource = null;

        function appendMessage(content, isUser = false, sourceNodes = [], followUpQuestions = [], isError = false, sourceMetadata = null, shouldScrollToBottom = true) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${isUser ? 'user-message' : 'assistant-message'} p-4 rounded-lg`;
            
            if (isUser) {
                messageDiv.textContent = content;
            } else {
                // Create main content area
                const contentArea = document.createElement('div');
                contentArea.className = 'main-content-area';
                contentArea.innerHTML = marked.parse(content);
                messageDiv.appendChild(contentArea);

                // Add sources section if there are source nodes
                if (sourceNodes && sourceNodes.length > 0) {
                    const sourcesSection = document.createElement('div');
                    sourcesSection.className = 'sources-section mt-4';
                    
                    // Track unique sources
                    const addedSources = new Set();
                    sourceNodes.forEach((source) => {
                        if (source && (source.snippet || source.text)) {
                            const sourceKey = `${source.metadata?.file_name || ''}-${source.metadata?.sharepoint_id || source.snippet || source.text}`;
                            addedSources.add(sourceKey);
                        }
                    });
                    
                    // Always show sources section if we have sourceNodes, even if filtered count is 0
                    const actualDisplayCount = addedSources.size;
                    
                    // Create sources header with metadata information
                    const sourcesHeader = document.createElement('div');
                    sourcesHeader.className = 'sources-header flex items-center justify-between cursor-pointer mb-2';
                    
                    // Build source count text with metadata
                    let sourceCountText = `Sources (${actualDisplayCount})`;
                    if (sourceMetadata && sourceMetadata.has_more_sources && actualDisplayCount < sourceMetadata.relevant_sources) {
                        sourceCountText += ` of ${sourceMetadata.relevant_sources} relevant`;
                    }
                    
                    sourcesHeader.innerHTML = `
                        <div class="flex items-center gap-2">
                            <span class="source-arrow expanded">▶</span>
                            <span>${sourceCountText}</span>
                        </div>
                        ${sourceMetadata?.has_more_sources ? '<span class="text-xs text-blue-600 font-medium">Show All Sources</span>' : ''}
                    `;
                    sourcesHeader.onclick = () => toggleSource(sourcesHeader);
                    sourcesSection.appendChild(sourcesHeader);

                    // Create sources content container
                    const sourcesContent = document.createElement('div');
                    sourcesContent.className = 'sources-content';

                    // Add each unique source with enhanced display
                    const displayedSources = new Set(); // Use separate set for tracking displayed sources
                    let actualSourcesDisplayed = 0;
                    
                    sourceNodes.forEach((source, index) => {
                        if (source && (source.snippet || source.text)) {
                            // Use metadata for better deduplication
                            const sourceKey = `${source.metadata?.file_name || ''}-${source.metadata?.sharepoint_id || source.snippet || source.text}`;
                            if (!displayedSources.has(sourceKey)) {
                                displayedSources.add(sourceKey);
                                actualSourcesDisplayed++;
                                
                                const sourceDiv = document.createElement('div');
                                sourceDiv.className = 'source-citation mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors';
                                
                                // Extract snippet (prefer backend snippet, fallback to truncated text)
                                const displayText = source.snippet || (source.text && source.text.length > 200 ? source.text.substring(0, 200) + '…' : source.text);
                                
                                // Build download link if web URL is available
                                const webUrl = source.metadata?.web_url || source.metadata?.webUrl;
                                const downloadLink = webUrl ? 
                                    `<a href="${webUrl}" target="_blank" class="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        Download
                                    </a>` : '';
                                
                                // Build relevance indicator if score is available
                                const relevanceIndicator = source.score ? 
                                    `<span class="inline-flex items-center gap-1 text-xs font-medium text-gray-600">
                                        <svg class="w-3 h-3 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                        ${Math.round(source.score * 100)}% match
                                    </span>` : '';
                                
                                sourceDiv.innerHTML = `
                                    <div class="flex items-start justify-between mb-2">
                                        <div class="font-semibold text-gray-900 flex-1">${source.metadata?.file_name || 'Untitled Document'}</div>
                                        <div class="flex items-center gap-2 ml-2">
                                            ${relevanceIndicator}
                                            ${downloadLink}
                                        </div>
                                    </div>
                                    <div class="text-sm text-gray-700 mb-2 line-height-relaxed">${displayText}</div>
                                    <div class="flex items-center gap-4 text-xs text-gray-500">
                                        ${source.metadata?.created_datetime ? `<span>Created: ${new Date(source.metadata.created_datetime).toLocaleDateString()}</span>` : ''}
                                        ${source.metadata?.last_modified_datetime ? `<span>Modified: ${new Date(source.metadata.last_modified_datetime).toLocaleDateString()}</span>` : ''}
                                        ${source.metadata?.parent_path ? `<span>Path: ${source.metadata.parent_path}</span>` : ''}
                                    </div>
                                `;
                                sourcesContent.appendChild(sourceDiv);
                            }
                        }
                    });

                    // If no sources were actually displayed, show a placeholder
                    if (actualSourcesDisplayed === 0) {
                        const noSourcesDiv = document.createElement('div');
                        noSourcesDiv.className = 'text-sm text-gray-500 p-4 text-center';
                        noSourcesDiv.textContent = 'No sources available for this response';
                        sourcesContent.appendChild(noSourcesDiv);
                    }

                    sourcesSection.appendChild(sourcesContent);
                    
                    // Add "Show More Sources" indicator if there are additional sources
                    if (sourceMetadata?.has_more_sources) {
                        const showMoreDiv = document.createElement('div');
                        showMoreDiv.className = 'mt-3 text-center';
                        showMoreDiv.innerHTML = `
                            <div class="text-sm text-gray-600 bg-gray-50 border border-gray-200 rounded-lg px-4 py-2 inline-flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>Showing top ${sourceMetadata.displayed_sources} of ${sourceMetadata.relevant_sources} relevant sources</span>
                            </div>
                        `;
                        sourcesSection.appendChild(showMoreDiv);
                    }
                    
                    // Always append the sources section
                    messageDiv.appendChild(sourcesSection);
                }

                // Add follow-up questions section if there are questions
                if (followUpQuestions && followUpQuestions.length > 0) {
                    const promptsSection = document.createElement('div');
                    promptsSection.className = 'prompts-section';
                    promptsSection.style.display = 'block';
                    
                    // Create prompts header
                    const promptsHeader = document.createElement('div');
                    promptsHeader.className = 'prompts-header';
                    promptsHeader.innerHTML = `
                        <span class="source-arrow expanded">▶</span>
                        <span>Recommended Questions</span>
                    `;
                    promptsHeader.onclick = () => toggleSource(promptsHeader);
                    promptsSection.appendChild(promptsHeader);

                    // Create prompts content
                    const promptsContent = document.createElement('div');
                    promptsContent.className = 'prompts-content';
                    promptsContent.style.display = 'block';

                    // Add each question
                    followUpQuestions.forEach(question => {
                        const promptDiv = document.createElement('div');
                        promptDiv.className = 'recommended-prompt';
                        promptDiv.textContent = question;
                        promptDiv.onclick = () => {
                            document.getElementById('queryInput').value = question;
                            // Trigger submit event that will handle scrolling
                            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                            document.getElementById('queryForm').dispatchEvent(submitEvent);
                        };
                        promptsContent.appendChild(promptDiv);
                    });

                    promptsSection.appendChild(promptsContent);
                    messageDiv.appendChild(promptsSection);
                }
            }
            
            messagesDiv.appendChild(messageDiv);
            if (shouldScrollToBottom) {
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
        }

        // Function to toggle source content visibility
        window.toggleSource = function(header) {
            const content = header.nextElementSibling;
            const arrow = header.querySelector('.source-arrow');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                arrow.classList.add('expanded');
            } else {
                content.style.display = 'none';
                arrow.classList.remove('expanded');
            }
        };

        // Streaming message functions
        function createStreamingMessage() {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-message assistant-message p-4 rounded-lg';

            // Create main content area with streaming indicator
            const contentArea = document.createElement('div');
            contentArea.className = 'main-content-area';
            contentArea.innerHTML = '<span class="streaming-cursor">▋</span>';
            messageDiv.appendChild(contentArea);

            messagesDiv.appendChild(messageDiv);

            // Return the message div for external scroll handling
            return messageDiv;
        }

        function updateStreamingMessage(messageDiv, fullText) {
            const contentArea = messageDiv.querySelector('.main-content-area');
            // Parse markdown and add cursor
            contentArea.innerHTML = marked.parse(fullText) + '<span class="streaming-cursor">▋</span>';
            
            // No automatic scrolling during streaming - let user read in place
        }

        function finalizeStreamingMessage(messageDiv, fullText, sourceNodes, followUpQuestions, sourceMetadata = null) {
            const contentArea = messageDiv.querySelector('.main-content-area');
            // Remove cursor and finalize content
            contentArea.innerHTML = marked.parse(fullText);
            
            // Add sources section if there are source nodes
            if (sourceNodes && sourceNodes.length > 0) {
                const sourcesSection = document.createElement('div');
                sourcesSection.className = 'sources-section mt-4';
                
                // Track unique sources
                const addedSources = new Set();
                sourceNodes.forEach((source) => {
                    if (source && (source.snippet || source.text)) {
                        const sourceKey = `${source.metadata?.file_name || ''}-${source.metadata?.sharepoint_id || source.snippet || source.text}`;
                        addedSources.add(sourceKey);
                    }
                });
                
                // Create sources header with metadata information
                const sourcesHeader = document.createElement('div');
                sourcesHeader.className = 'sources-header flex items-center justify-between cursor-pointer mb-2';
                
                // Build source count text with metadata
                let sourceCountText = `Sources (${addedSources.size})`;
                if (sourceMetadata && sourceMetadata.has_more_sources && addedSources.size < sourceMetadata.relevant_sources) {
                    sourceCountText += ` of ${sourceMetadata.relevant_sources} relevant`;
                }
                
                sourcesHeader.innerHTML = `
                    <div class="flex items-center gap-2">
                        <span class="source-arrow expanded">▶</span>
                        <span>${sourceCountText}</span>
                    </div>
                    ${sourceMetadata?.has_more_sources ? '<span class="text-xs text-blue-600 font-medium">Show All Sources</span>' : ''}
                `;
                sourcesHeader.onclick = () => toggleSource(sourcesHeader);
                sourcesSection.appendChild(sourcesHeader);

                // Create sources content container
                const sourcesContent = document.createElement('div');
                sourcesContent.className = 'sources-content';

                // Add each unique source
                const displayedSources = new Set(); // Use separate set for tracking displayed sources
                let actualSourcesDisplayed = 0;
                
                sourceNodes.forEach((source, index) => {
                    if (source && (source.snippet || source.text)) {
                        // Use metadata for better deduplication
                        const sourceKey = `${source.metadata?.file_name || ''}-${source.metadata?.sharepoint_id || source.snippet || source.text}`;
                        if (!displayedSources.has(sourceKey)) {
                            displayedSources.add(sourceKey);
                            actualSourcesDisplayed++;
                            
                            const sourceDiv = document.createElement('div');
                            sourceDiv.className = 'source-citation mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors';
                            
                            // Extract snippet (prefer backend snippet, fallback to truncated text)
                            const displayText = source.snippet || (source.text && source.text.length > 200 ? source.text.substring(0, 200) + '…' : source.text);
                            
                            // Build download link if web URL is available
                            const webUrl = source.metadata?.web_url || source.metadata?.webUrl;
                            const downloadLink = webUrl ? 
                                `<a href="${webUrl}" target="_blank" class="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Download
                                </a>` : '';
                            
                            // Build relevance indicator if score is available
                            const relevanceIndicator = source.score ? 
                                `<span class="inline-flex items-center gap-1 text-xs font-medium text-gray-600">
                                    <svg class="w-3 h-3 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    ${Math.round(source.score * 100)}% match
                                </span>` : '';
                            
                            sourceDiv.innerHTML = `
                                <div class="flex items-start justify-between mb-2">
                                    <div class="font-semibold text-gray-900 flex-1">${source.metadata?.file_name || 'Untitled Document'}</div>
                                    <div class="flex items-center gap-2 ml-2">
                                        ${relevanceIndicator}
                                        ${downloadLink}
                                    </div>
                                </div>
                                <div class="text-sm text-gray-700 mb-2 line-height-relaxed">${displayText}</div>
                                <div class="flex items-center gap-4 text-xs text-gray-500">
                                    ${source.metadata?.created_datetime ? `<span>Created: ${new Date(source.metadata.created_datetime).toLocaleDateString()}</span>` : ''}
                                    ${source.metadata?.last_modified_datetime ? `<span>Modified: ${new Date(source.metadata.last_modified_datetime).toLocaleDateString()}</span>` : ''}
                                    ${source.metadata?.parent_path ? `<span>Path: ${source.metadata.parent_path}</span>` : ''}
                                </div>
                            `;
                            sourcesContent.appendChild(sourceDiv);
                        }
                    }
                });

                // If no sources were actually displayed, show a placeholder
                if (actualSourcesDisplayed === 0) {
                    const noSourcesDiv = document.createElement('div');
                    noSourcesDiv.className = 'text-sm text-gray-500 p-4 text-center';
                    noSourcesDiv.textContent = 'No sources available for this response';
                    sourcesContent.appendChild(noSourcesDiv);
                }

                sourcesSection.appendChild(sourcesContent);
                
                // Add "Show More Sources" indicator if there are additional sources
                if (sourceMetadata?.has_more_sources) {
                    const showMoreDiv = document.createElement('div');
                    showMoreDiv.className = 'mt-3 text-center';
                    showMoreDiv.innerHTML = `
                        <div class="text-sm text-gray-600 bg-gray-50 border border-gray-200 rounded-lg px-4 py-2 inline-flex items-center gap-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>Showing top ${sourceMetadata.displayed_sources} of ${sourceMetadata.relevant_sources} relevant sources</span>
                        </div>
                    `;
                    sourcesSection.appendChild(showMoreDiv);
                }
                
                // Always append the sources section
                messageDiv.appendChild(sourcesSection);
            }

            // Add follow-up questions section if there are questions
            if (followUpQuestions && followUpQuestions.length > 0) {
                const promptsSection = document.createElement('div');
                promptsSection.className = 'prompts-section';
                promptsSection.style.display = 'block';
                
                // Create prompts header
                const promptsHeader = document.createElement('div');
                promptsHeader.className = 'prompts-header';
                promptsHeader.innerHTML = `
                    <span class="source-arrow expanded">▶</span>
                    <span>Recommended Questions</span>
                `;
                promptsHeader.onclick = () => toggleSource(promptsHeader);
                promptsSection.appendChild(promptsHeader);

                // Create prompts content
                const promptsContent = document.createElement('div');
                promptsContent.className = 'prompts-content';
                promptsContent.style.display = 'block';

                // Add each question
                followUpQuestions.forEach(question => {
                    const promptDiv = document.createElement('div');
                    promptDiv.className = 'recommended-prompt';
                    promptDiv.textContent = question;
                    promptDiv.onclick = () => {
                        document.getElementById('queryInput').value = question;
                        // Trigger submit event that will handle scrolling
                        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                        document.getElementById('queryForm').dispatchEvent(submitEvent);
                    };
                    promptsContent.appendChild(promptDiv);
                });

                promptsSection.appendChild(promptsContent);
                messageDiv.appendChild(promptsSection);
            }
            
            // Optional: Only scroll to show sources if user hasn't manually scrolled
            // Let user decide when to scroll to see sources and questions
        }

        // Smart scrolling functions
        function scrollToShowNewMessage(messageDiv) {
            const messagesDiv = document.getElementById('chatMessages');
            // Scroll to show the start of the new message only once at the beginning
            messageDiv.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        // Handle query form submission with streaming
        document.getElementById('queryForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const queryInput = document.getElementById('queryInput');
            const query = queryInput.value.trim();
            if (!query) return;

            appendMessage(query, true, [], [], false, null, false); // Display user query, don't scroll to bottom
            queryInput.value = ''; // Clear input

            // Create a streaming message container first
            const streamingMessageDiv = createStreamingMessage();

            // Scroll the new response to the top of the chat window
            requestAnimationFrame(() => {
                streamingMessageDiv.scrollIntoView({ behavior: 'smooth', block: 'start' });
            });

            // Start streaming response
            try {
                const response = await fetch(`${window.__RAG_BASE}/query/stream?query=${encodeURIComponent(query)}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/plain',
                        'Cache-Control': 'no-cache'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                let fullResponse = '';
                let sourceNodes = [];
                let followUpQuestions = [];
                let sourceMetadata = null;

                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value, { stream: true });
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));
                                    
                                    if (data.type === 'chunk') {
                                        fullResponse += data.content;
                                        updateStreamingMessage(streamingMessageDiv, fullResponse);
                                    } else if (data.type === 'complete') {
                                        sourceNodes = data.source_nodes || [];
                                        followUpQuestions = data.follow_up_questions || [];
                                        sourceMetadata = data.source_metadata || null;
                                        finalizeStreamingMessage(streamingMessageDiv, fullResponse, sourceNodes, followUpQuestions, sourceMetadata);
                                    } else if (data.type === 'error') {
                                        throw new Error(data.content);
                                    }
                                } catch (parseError) {
                                    console.warn('Failed to parse streaming data:', parseError);
                                }
                            }
                        }
                    }
                } finally {
                    reader.releaseLock();
                }

            } catch (error) {
                console.error('Streaming query error:', error);
                appendMessage(`Error: ${error.message}`, false, [], [], true);
            }
        });

        // New chat button
        document.getElementById('newChatBtn').addEventListener('click', () => {
            document.getElementById('chatMessages').innerHTML = '';
        });

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', async () => {
            try {
                await fetch(`${window.__RAG_BASE}/logout`, { method: 'POST', credentials: 'include' });
                document.getElementById('loginScreen').style.display = 'flex';
                document.getElementById('mainContent').style.display = 'none';
            } catch (error) {
                console.error('Logout error:', error);
            }
        });

        // Add dark mode toggle functionality
        document.addEventListener('DOMContentLoaded', () => {
            const darkModeToggle = document.getElementById('darkModeToggle');
            const sunIcon = darkModeToggle.querySelector('.sun-icon');
            const moonIcon = darkModeToggle.querySelector('.moon-icon');

            // Check for saved theme preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
                sunIcon.classList.remove('hidden');
                moonIcon.classList.add('hidden');
            }

            darkModeToggle.addEventListener('click', () => {
                const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
                
                if (isDark) {
                    document.documentElement.removeAttribute('data-theme');
                    localStorage.setItem('theme', 'light');
                    sunIcon.classList.add('hidden');
                    moonIcon.classList.remove('hidden');
                } else {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    localStorage.setItem('theme', 'dark');
                    sunIcon.classList.remove('hidden');
                    moonIcon.classList.add('hidden');
                }
            });
        });

        // Function to refresh documents periodically
        function startDocumentRefresh() {
            // Initial load
            loadDocuments();
            
            // Refresh every 30 seconds
            setInterval(loadDocuments, 30000);
        }
    </script>
</body>
</html> 